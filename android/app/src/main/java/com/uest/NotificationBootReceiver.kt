package com.uest

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class NotificationBootReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d("NotificationBootReceiver", "Boot completed, action: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                Log.d("NotificationBootReceiver", "Device booted, recreating notification channels")
                
                // Recreate notification channels after boot
                NotificationChannelManager.createNotificationChannels(context)
                
                // You can also reschedule any pending notifications here if needed
                Log.d("NotificationBootReceiver", "Notification channels recreated after boot")
            }
        }
    }
}
