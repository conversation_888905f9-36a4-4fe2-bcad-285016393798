package com.uest

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.NotificationManagerCompat
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule

class NotificationPermissionModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "NotificationPermissionModule"
    }

    @ReactMethod
    fun areNotificationsEnabled(promise: Promise) {
        try {
            val context = reactApplicationContext
            val hasPermission = NotificationHelper.hasNotificationPermission(context)
            val areEnabled = NotificationHelper.areNotificationsEnabled(context)

            val result = Arguments.createMap()
            result.putBoolean("enabled", areEnabled)
            result.putBoolean("hasPermission", hasPermission)
            result.putString("status", when {
                hasPermission && areEnabled -> "granted"
                hasPermission && !areEnabled -> "granted_but_disabled"
                else -> "denied"
            })

            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to check notification permission", e)
        }
    }

    @ReactMethod
    fun openNotificationSettings(promise: Promise) {
        try {
            val context = reactApplicationContext
            val intent = Intent()
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // For Android 8.0 and above, open app-specific notification settings
                intent.action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
            } else {
                // For older versions, open general app settings
                intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                intent.data = Uri.parse("package:${context.packageName}")
            }
            
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to open notification settings", e)
        }
    }

    @ReactMethod
    fun getChannelSettings(channelId: String, promise: Promise) {
        try {
            val context = reactApplicationContext
            val enabled = NotificationHelper.isChannelEnabled(context, channelId)

            val result = Arguments.createMap()
            result.putString("channelId", channelId)
            result.putBoolean("enabled", enabled)

            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to get channel settings", e)
        }
    }

    @ReactMethod
    fun getDetailedStatus(promise: Promise) {
        try {
            val context = reactApplicationContext
            val status = NotificationHelper.getDetailedStatus(context)

            val result = Arguments.createMap()
            for ((key, value) in status) {
                when (value) {
                    is Boolean -> result.putBoolean(key, value)
                    is Int -> result.putInt(key, value)
                    is String -> result.putString(key, value)
                    is List<*> -> {
                        val array = Arguments.createArray()
                        value.forEach { item ->
                            when (item) {
                                is String -> array.pushString(item)
                                is Boolean -> array.pushBoolean(item)
                                is Int -> array.pushInt(item)
                            }
                        }
                        result.putArray(key, array)
                    }
                    is Map<*, *> -> {
                        val map = Arguments.createMap()
                        value.forEach { (k, v) ->
                            when (v) {
                                is Boolean -> map.putBoolean(k.toString(), v)
                                is String -> map.putString(k.toString(), v)
                                is Int -> map.putInt(k.toString(), v)
                            }
                        }
                        result.putMap(key, map)
                    }
                }
            }

            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to get detailed status", e)
        }
    }

    @ReactMethod
    fun initializeNotifications(promise: Promise) {
        try {
            val context = reactApplicationContext
            val success = NotificationHelper.initializeNotifications(context)

            val result = Arguments.createMap()
            result.putBoolean("success", success)
            result.putString("message", if (success) "Notifications initialized successfully" else "Failed to initialize notifications")

            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to initialize notifications", e)
        }
    }

    @ReactMethod
    fun requestNotificationPermission(promise: Promise) {
        try {
            val context = reactApplicationContext
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // For Android 13+, we need to request POST_NOTIFICATIONS permission
                // This should be handled by the React Native PermissionsAndroid API
                val notificationManager = NotificationManagerCompat.from(context)
                val enabled = notificationManager.areNotificationsEnabled()
                
                val result = Arguments.createMap()
                result.putBoolean("granted", enabled)
                result.putString("status", if (enabled) "granted" else "denied")
                
                promise.resolve(result)
            } else {
                // For older versions, notifications are enabled by default
                val result = Arguments.createMap()
                result.putBoolean("granted", true)
                result.putString("status", "granted")
                
                promise.resolve(result)
            }
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to request notification permission", e)
        }
    }

    @ReactMethod
    fun createTestNotification(title: String, body: String, promise: Promise) {
        try {
            val context = reactApplicationContext
            val notificationManager = NotificationManagerCompat.from(context)
            
            if (!notificationManager.areNotificationsEnabled()) {
                promise.reject("PERMISSION_DENIED", "Notifications are not enabled")
                return
            }

            // Create a test notification using the existing notification system
            val intent = Intent(context, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            
            val pendingIntent = android.app.PendingIntent.getActivity(
                context, 0, intent,
                android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val notificationBuilder = androidx.core.app.NotificationCompat.Builder(context, NotificationChannelManager.CHANNEL_ID_GENERAL)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setContentText(body)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setPriority(androidx.core.app.NotificationCompat.PRIORITY_HIGH)

            val notificationId = System.currentTimeMillis().toInt()
            notificationManager.notify(notificationId, notificationBuilder.build())
            
            promise.resolve(notificationId)
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to create test notification", e)
        }
    }
}
