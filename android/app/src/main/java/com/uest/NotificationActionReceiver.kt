package com.uest

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class NotificationActionReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d("NotificationActionReceiver", "Notification action received: ${intent.action}")
        
        when (intent.action) {
            "com.uest.NOTIFICATION_ACTION" -> {
                val notificationId = intent.getIntExtra("notification_id", -1)
                val actionType = intent.getStringExtra("action_type")
                
                Log.d("NotificationActionReceiver", "Action: $actionType, ID: $notificationId")
                
                // Handle different notification actions
                when (actionType) {
                    "OPEN_APP" -> {
                        // Open the main app
                        val launchIntent = context.packageManager.getLaunchIntentForPackage(context.packageName)
                        launchIntent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        context.startActivity(launchIntent)
                    }
                    "DISMISS" -> {
                        // Handle dismiss action
                        Log.d("NotificationActionReceiver", "Notification dismissed")
                    }
                    else -> {
                        Log.d("NotificationActionReceiver", "Unknown action: $actionType")
                    }
                }
            }
        }
    }
}
