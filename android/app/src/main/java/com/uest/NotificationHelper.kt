package com.uest

import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat

object NotificationHelper {
    
    /**
     * Check if the app has notification permission
     */
    fun hasNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ requires POST_NOTIFICATIONS permission
            ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // For older versions, check if notifications are enabled
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
    }
    
    /**
     * Check if notifications are enabled at the system level
     */
    fun areNotificationsEnabled(context: Context): <PERSON><PERSON><PERSON> {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
    
    /**
     * Check if a specific notification channel is enabled
     */
    fun isChannelEnabled(context: Context, channelId: String): <PERSON>ole<PERSON> {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val channel = notificationManager.getNotificationChannel(channelId)
            return channel?.importance != NotificationManager.IMPORTANCE_NONE
        }
        return areNotificationsEnabled(context)
    }
    
    /**
     * Get all notification channels and their status
     */
    fun getChannelStatuses(context: Context): Map<String, Boolean> {
        val statuses = mutableMapOf<String, Boolean>()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Check our app's channels
            val channels = listOf(
                NotificationChannelManager.CHANNEL_ID_GENERAL,
                NotificationChannelManager.CHANNEL_ID_QUIZ,
                NotificationChannelManager.CHANNEL_ID_URGENT
            )
            
            for (channelId in channels) {
                val channel = notificationManager.getNotificationChannel(channelId)
                statuses[channelId] = channel?.importance != NotificationManager.IMPORTANCE_NONE
            }
        } else {
            // For older versions, all channels have the same status as the app
            val enabled = areNotificationsEnabled(context)
            statuses[NotificationChannelManager.CHANNEL_ID_GENERAL] = enabled
            statuses[NotificationChannelManager.CHANNEL_ID_QUIZ] = enabled
            statuses[NotificationChannelManager.CHANNEL_ID_URGENT] = enabled
        }
        
        return statuses
    }
    
    /**
     * Initialize notification system
     */
    fun initializeNotifications(context: Context): Boolean {
        return try {
            // Create notification channels
            NotificationChannelManager.createNotificationChannels(context)
            
            // Verify channels were created
            val channelStatuses = getChannelStatuses(context)
            val hasEnabledChannels = channelStatuses.values.any { it }
            
            android.util.Log.d("NotificationHelper", "Notification initialization complete. Has enabled channels: $hasEnabledChannels")
            android.util.Log.d("NotificationHelper", "Channel statuses: $channelStatuses")
            
            true
        } catch (e: Exception) {
            android.util.Log.e("NotificationHelper", "Failed to initialize notifications", e)
            false
        }
    }
    
    /**
     * Get detailed notification status for debugging
     */
    fun getDetailedStatus(context: Context): Map<String, Any> {
        val status = mutableMapOf<String, Any>()
        
        status["hasPermission"] = hasNotificationPermission(context)
        status["areEnabled"] = areNotificationsEnabled(context)
        status["channelStatuses"] = getChannelStatuses(context)
        status["androidVersion"] = Build.VERSION.SDK_INT
        status["packageName"] = context.packageName
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val channels = notificationManager.notificationChannels
            status["totalChannels"] = channels.size
            status["channelNames"] = channels.map { "${it.id}: ${it.name}" }
        }
        
        return status
    }
}
