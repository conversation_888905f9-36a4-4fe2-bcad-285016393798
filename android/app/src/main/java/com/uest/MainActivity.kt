package com.uest

import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import android.os.Bundle
import android.content.Intent
import android.util.Log
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.os.Build
import org.devio.rn.splashscreen.SplashScreen

class MainActivity : ReactActivity() {

  companion object {
    const val NOTIFICATION_PERMISSION_REQUEST_CODE = 1001
    private var permissionPromise: com.facebook.react.bridge.Promise? = null
  }

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    // Ensure notification channels are created when app starts
    NotificationChannelManager.createNotificationChannels(this)

    // Handle notification click intent
    handleNotificationIntent(intent)
  }

  override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    intent?.let { handleNotificationIntent(it) }
  }

  private fun handleNotificationIntent(intent: Intent) {
    // Check if app was opened from a notification
    if (intent.hasExtra("notification_data")) {
      val notificationData = intent.getStringExtra("notification_data")
      Log.d("MainActivity", "App opened from notification: $notificationData")

      // You can handle notification data here
      // For example, navigate to specific screen based on notification type
    }
  }

  override fun onRequestPermissionsResult(
    requestCode: Int,
    permissions: Array<out String>,
    grantResults: IntArray
  ) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults)

    when (requestCode) {
      NOTIFICATION_PERMISSION_REQUEST_CODE -> {
        val granted = grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED

        permissionPromise?.let { promise ->
          val result = com.facebook.react.bridge.Arguments.createMap()
          result.putBoolean("granted", granted)
          result.putString("status", if (granted) "granted" else "denied")
          promise.resolve(result)
          permissionPromise = null
        }
      }
    }
  }

  fun requestNotificationPermissionFromActivity(promise: com.facebook.react.bridge.Promise) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
      val permission = android.Manifest.permission.POST_NOTIFICATIONS

      if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
        // Permission already granted
        val result = com.facebook.react.bridge.Arguments.createMap()
        result.putBoolean("granted", true)
        result.putString("status", "granted")
        promise.resolve(result)
      } else {
        // Store the promise to resolve it later
        permissionPromise = promise

        // Request permission
        ActivityCompat.requestPermissions(
          this,
          arrayOf(permission),
          NOTIFICATION_PERMISSION_REQUEST_CODE
        )
      }
    } else {
      // For older versions, notifications are enabled by default
      val result = com.facebook.react.bridge.Arguments.createMap()
      result.putBoolean("granted", true)
      result.putString("status", "granted")
      promise.resolve(result)
    }
  }

  override fun getMainComponentName(): String = "UEST"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
