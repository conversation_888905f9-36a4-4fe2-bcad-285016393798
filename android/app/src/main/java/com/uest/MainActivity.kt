package com.uest

import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import android.os.Bundle
import android.content.Intent
import android.util.Log
import org.devio.rn.splashscreen.SplashScreen

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    // Ensure notification channels are created when app starts
    NotificationChannelManager.createNotificationChannels(this)

    // Handle notification click intent
    handleNotificationIntent(intent)
  }

  override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    intent?.let { handleNotificationIntent(it) }
  }

  private fun handleNotificationIntent(intent: Intent) {
    // Check if app was opened from a notification
    if (intent.hasExtra("notification_data")) {
      val notificationData = intent.getStringExtra("notification_data")
      Log.d("MainActivity", "App opened from notification: $notificationData")

      // You can handle notification data here
      // For example, navigate to specific screen based on notification type
    }
  }

  override fun getMainComponentName(): String = "UEST"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
