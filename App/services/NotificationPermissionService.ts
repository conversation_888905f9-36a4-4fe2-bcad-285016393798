import { NativeModules, Platform } from 'react-native';

const { NotificationPermissionModule } = NativeModules;

export interface NotificationPermissionStatus {
  enabled: boolean;
  hasPermission?: boolean;
  status: 'granted' | 'denied' | 'granted_but_disabled' | 'unknown';
}

export interface ChannelSettings {
  channelId: string;
  enabled: boolean;
}

class NotificationPermissionService {
  /**
   * Check if notifications are enabled for the app
   */
  async areNotificationsEnabled(): Promise<NotificationPermissionStatus> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.areNotificationsEnabled();
        return result;
      }
      
      // For iOS or if module is not available, assume enabled
      return { enabled: true, status: 'granted' };
    } catch (error) {
      console.error('Error checking notification permission:', error);
      return { enabled: false, status: 'unknown' };
    }
  }

  /**
   * Open the device notification settings for this app
   */
  async openNotificationSettings(): Promise<boolean> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        await NotificationPermissionModule.openNotificationSettings();
        return true;
      }
      
      // For iOS, use the standard Linking approach
      const { Linking } = require('react-native');
      await Linking.openURL('app-settings:');
      return true;
    } catch (error) {
      console.error('Error opening notification settings:', error);
      return false;
    }
  }

  /**
   * Get settings for a specific notification channel
   */
  async getChannelSettings(channelId: string): Promise<ChannelSettings | null> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.getChannelSettings(channelId);
        return result;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting channel settings:', error);
      return null;
    }
  }

  /**
   * Request notification permission (mainly for Android 13+)
   */
  async requestNotificationPermission(): Promise<NotificationPermissionStatus> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.requestNotificationPermission();
        return result;
      }
      
      return { enabled: true, status: 'granted' };
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return { enabled: false, status: 'unknown' };
    }
  }

  /**
   * Create a test notification to verify the system is working
   */
  async createTestNotification(title: string = 'UEST Test', body: string = 'Test notification from UEST app'): Promise<number | null> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const notificationId = await NotificationPermissionModule.createTestNotification(title, body);
        return notificationId;
      }
      
      return null;
    } catch (error) {
      console.error('Error creating test notification:', error);
      throw error;
    }
  }

  /**
   * Initialize notification system
   */
  async initializeNotifications(): Promise<{ success: boolean; message: string }> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.initializeNotifications();
        return result;
      }

      return { success: true, message: 'iOS notifications initialized' };
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return { success: false, message: 'Failed to initialize notifications' };
    }
  }

  /**
   * Get detailed notification status from native module
   */
  async getDetailedStatus(): Promise<any> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.getDetailedStatus();
        return result;
      }

      return null;
    } catch (error) {
      console.error('Error getting detailed status:', error);
      return null;
    }
  }

  /**
   * Check if the app can send notifications and provide detailed status
   */
  async getDetailedNotificationStatus(): Promise<{
    appNotificationsEnabled: boolean;
    hasPermission: boolean;
    channelStatuses: { [key: string]: boolean };
    canSendNotifications: boolean;
    recommendedAction?: string;
    nativeStatus?: any;
  }> {
    try {
      const appStatus = await this.areNotificationsEnabled();
      const nativeStatus = await this.getDetailedStatus();
      const channelStatuses: { [key: string]: boolean } = {};

      // Check main channels
      const channels = ['classwork-channel-v3', 'quiz-channel', 'urgent-channel'];

      for (const channelId of channels) {
        const channelStatus = await this.getChannelSettings(channelId);
        channelStatuses[channelId] = channelStatus?.enabled ?? false;
      }

      const hasPermission = appStatus.hasPermission ?? true;
      const canSendNotifications = appStatus.enabled && Object.values(channelStatuses).some(enabled => enabled);

      let recommendedAction: string | undefined;
      if (!hasPermission) {
        recommendedAction = 'Grant notification permission in app settings';
      } else if (!appStatus.enabled) {
        recommendedAction = 'Enable notifications in app settings';
      } else if (!canSendNotifications) {
        recommendedAction = 'Enable notification channels in app settings';
      }

      return {
        appNotificationsEnabled: appStatus.enabled,
        hasPermission,
        channelStatuses,
        canSendNotifications,
        recommendedAction,
        nativeStatus,
      };
    } catch (error) {
      console.error('Error getting detailed notification status:', error);
      return {
        appNotificationsEnabled: false,
        hasPermission: false,
        channelStatuses: {},
        canSendNotifications: false,
        recommendedAction: 'Check notification settings manually',
      };
    }
  }
}

export default new NotificationPermissionService();
