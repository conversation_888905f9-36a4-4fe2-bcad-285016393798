import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
  NativeModules,
} from 'react-native';
import { useNotificationPermission } from '../hooks/useNotificationPermission';
import NotificationPermissionDialog from '../CommonComponents/NotificationPermissionDialog';

const NotificationPermissionTest = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    showPermissionDialog,
    isRequestingPermission,
    checkNotificationPermission,
    requestNotificationPermission,
    handleDialogCancel,
    handleDialogOpenSettings,
  } = useNotificationPermission();

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testPermissionCheck = async () => {
    setIsLoading(true);
    addTestResult('Testing permission check...');
    
    try {
      const hasPermission = await checkNotificationPermission();
      addTestResult(`Permission check result: ${hasPermission ? 'GRANTED' : 'DENIED'}`);
    } catch (error) {
      addTestResult(`Permission check error: ${error}`);
    }
    
    setIsLoading(false);
  };

  const testPermissionRequest = async () => {
    setIsLoading(true);
    addTestResult('Testing permission request...');
    
    try {
      const granted = await requestNotificationPermission();
      addTestResult(`Permission request result: ${granted ? 'GRANTED' : 'DENIED'}`);
    } catch (error) {
      addTestResult(`Permission request error: ${error}`);
    }
    
    setIsLoading(false);
  };

  const testNativeModule = async () => {
    setIsLoading(true);
    addTestResult('Testing native module...');
    
    try {
      const { NotificationPermissionModule } = NativeModules;
      
      if (!NotificationPermissionModule) {
        addTestResult('Native module not available');
        setIsLoading(false);
        return;
      }

      // Test areNotificationsEnabled
      const enabledResult = await NotificationPermissionModule.areNotificationsEnabled();
      addTestResult(`areNotificationsEnabled: ${JSON.stringify(enabledResult)}`);

      // Test getDetailedStatus
      const detailedResult = await NotificationPermissionModule.getDetailedStatus();
      addTestResult(`getDetailedStatus: ${JSON.stringify(detailedResult)}`);

      // Test requestNotificationPermission
      const requestResult = await NotificationPermissionModule.requestNotificationPermission();
      addTestResult(`requestNotificationPermission: ${JSON.stringify(requestResult)}`);

    } catch (error) {
      addTestResult(`Native module error: ${error}`);
    }
    
    setIsLoading(false);
  };

  const testCreateNotification = async () => {
    setIsLoading(true);
    addTestResult('Testing notification creation...');
    
    try {
      const { NotificationPermissionModule } = NativeModules;
      
      if (!NotificationPermissionModule) {
        addTestResult('Native module not available for notification test');
        setIsLoading(false);
        return;
      }

      const notificationId = await NotificationPermissionModule.createTestNotification(
        'Test Notification',
        'This is a test notification from UEST app'
      );
      
      addTestResult(`Test notification created with ID: ${notificationId}`);
    } catch (error) {
      addTestResult(`Notification creation error: ${error}`);
    }
    
    setIsLoading(false);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Notification Permission Test</Text>
      
      <Text style={styles.info}>
        Platform: {Platform.OS} {Platform.Version}
      </Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testPermissionCheck}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Permission Check</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testPermissionRequest}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Permission Request</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testNativeModule}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Native Module</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testCreateNotification}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Create Notification</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </View>

      <NotificationPermissionDialog
        visible={showPermissionDialog}
        onCancel={handleDialogCancel}
        onOpenSettings={handleDialogOpenSettings}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  info: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#FD904B',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  clearButton: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    minHeight: 200,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 14,
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});

export default NotificationPermissionTest;
