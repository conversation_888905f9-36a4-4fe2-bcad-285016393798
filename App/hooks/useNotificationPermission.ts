import { useState, useCallback } from 'react';
import { Platform, PermissionsAndroid, NativeModules } from 'react-native';
import { setupNotifications } from '../NotificationHelper/NotificationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useNotificationPermission = () => {
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  // Check if notifications are enabled
  const checkNotificationPermission = useCallback(async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      try {
        const { NotificationPermissionModule } = NativeModules;

        if (NotificationPermissionModule) {
          const result = await NotificationPermissionModule.areNotificationsEnabled();
          return result && result.enabled;
        } else {
          // Fallback for Android 13+
          if (Platform.Version >= 33) {
            const granted = await PermissionsAndroid.check(
              PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
            );
            return granted;
          }
          return true;
        }
      } catch (error) {
        console.error('Error checking notification permission:', error);

        // Final fallback
        if (Platform.Version >= 33) {
          try {
            const granted = await PermissionsAndroid.check(
              PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
            );
            return granted;
          } catch (fallbackError) {
            return false;
          }
        }
        return true;
      }
    }
    // For iOS, assume permissions are granted (handled by setupNotifications)
    return true;
  }, []);

  // Request notification permission with dialog
  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    if (isRequestingPermission) {
      return false;
    }

    setIsRequestingPermission(true);

    try {
      // First check if we already have permission
      const hasPermission = await checkNotificationPermission();
      if (hasPermission) {
        setIsRequestingPermission(false);
        return true;
      }

      // Try to setup notifications
      const setupSuccess = await setupNotifications(false);
      
      if (setupSuccess) {
        // Permission granted
        await AsyncStorage.setItem('notificationsEnabled', JSON.stringify(true));
        setIsRequestingPermission(false);
        return true;
      } else {
        // Permission denied, show dialog
        setShowPermissionDialog(true);
        setIsRequestingPermission(false);
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setIsRequestingPermission(false);
      return false;
    }
  }, [isRequestingPermission, checkNotificationPermission]);

  // Handle dialog actions
  const handleDialogCancel = useCallback(() => {
    setShowPermissionDialog(false);
  }, []);

  const handleDialogOpenSettings = useCallback(() => {
    setShowPermissionDialog(false);
    // The dialog component will handle opening settings
  }, []);

  return {
    showPermissionDialog,
    isRequestingPermission,
    checkNotificationPermission,
    requestNotificationPermission,
    handleDialogCancel,
    handleDialogOpenSettings,
  };
};
