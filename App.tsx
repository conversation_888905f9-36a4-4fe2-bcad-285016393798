import React, {useEffect} from 'react';
import AppNavigator from './App/route/AppNavigator';
import {NavigationContainer} from '@react-navigation/native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Provider} from 'react-redux';
import {store} from './App/Redux/store';
import SplashScreen from 'react-native-splash-screen';
import {setupNotifications} from './App/NotificationHelper/NotificationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

function App(): React.JSX.Element {
  useEffect(() => {
    // Hide splash screen
    SplashScreen.hide();

    const initNotifications = async () => {
      try {
        await setupNotifications(false);

        // Check if we need to register FCM token with server
        const isTokenRegistered = await AsyncStorage.getItem('fcmTokenRegistered');
        if (!isTokenRegistered) {
          console.log('FCM token not registered with server yet');
          // Token registration will happen in NotificationService.js onRegister callback
        }
      } catch (error) {
        console.error('Failed to initialize notifications:', error);
      }
    };

    initNotifications();
  }, []);

  return (
    <Provider store={store}>
      <GestureHandlerRootView>
        <NavigationContainer>
          <AppNavigator />
        </NavigationContainer>
      </GestureHandlerRootView>
    </Provider>
  );
}

export default App;
